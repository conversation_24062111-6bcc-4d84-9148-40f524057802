#!/bin/bash

# Script para verificar que Moodle sea accesible desde cualquier IP
echo "🔍 VERIFICACIÓN DE ACCESO A MOODLE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# 1. Verificar servicios
echo "1. 🔧 ESTADO DE SERVICIOS:"
echo "   Apache: $(systemctl is-active apache2)"
echo "   MySQL: $(systemctl is-active mysql)"
echo ""

# 2. Verificar IPs del servidor
echo "2. 🌐 IPs DEL SERVIDOR:"
ip addr show | grep -E "inet [0-9]" | grep -v "127.0.0.1" | awk '{print "   " $2}' | sed 's/\/.*$//'
echo ""

# 3. Verificar configuración de Apache
echo "3. ⚙️  CONFIGURACIÓN DE APACHE:"
echo "   Virtual Hosts activos:"
sudo apache2ctl -S 2>/dev/null | grep "port 80" | head -3
echo ""

# 4. Verificar acceso local a Moodle
echo "4. 🏠 PRUEBA DE ACCESO LOCAL:"
if curl -s -o /dev/null -w "%{http_code}" http://localhost/moodle | grep -q "200\|302\|301"; then
    echo "   ✅ Moodle responde en localhost"
else
    echo "   ❌ Moodle NO responde en localhost"
fi
echo ""

# 5. Verificar configuración de Moodle
echo "5. 📝 CONFIGURACIÓN DE MOODLE:"
echo "   wwwroot configurado como:"
sudo grep "wwwroot" /var/www/html/moodle/config.php | head -2 | sed 's/^/   /'
echo ""

# 6. Mostrar URLs de acceso
echo "6. 🔗 URLS DE ACCESO DISPONIBLES:"
echo "   Desde la red local:"
ip addr show | grep -E "inet [0-9]" | grep -v "127.0.0.1" | awk '{print "   • http://" $2 "/moodle"}' | sed 's/\/[0-9]*\/moodle/\/moodle/'
echo "   Desde el servidor:"
echo "   • http://localhost/moodle"
echo ""

# 7. Verificar permisos
echo "7. 🔐 PERMISOS DE ARCHIVOS:"
echo "   Moodle directory: $(ls -ld /var/www/html/moodle | awk '{print $1 " " $3 ":" $4}')"
echo "   Moodledata directory: $(ls -ld /var/www/moodledata | awk '{print $1 " " $3 ":" $4}')"
echo ""

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ VERIFICACIÓN COMPLETADA"
echo ""
echo "📱 INSTRUCCIONES PARA ESTUDIANTES:"
echo "   1. Conectarse al WiFi de la institución"
echo "   2. Abrir navegador web"
echo "   3. Ir a: http://[IP_DEL_SERVIDOR]/moodle"
echo "   4. Usar cualquiera de las IPs mostradas arriba"
echo ""
echo "🆘 Si hay problemas:"
echo "   • Verificar que el router/WiFi esté funcionando"
echo "   • Verificar que el servidor esté en la misma red"
echo "   • Ejecutar: sudo systemctl restart apache2"
echo ""