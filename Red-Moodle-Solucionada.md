# 🌐 Red Moodle - Problema <PERSON>uc<PERSON>ado

**Fecha:** 30 de Julio, 2025  
**Problema:** Estudiantes no podían acceder al servidor Moodle desde tablets WiFi

## 🔍 Diagnóstico del Problema

### Situación Original:
- **Servidor configurado:** ************* (red inexistente)
- **Router funcionando:** *********** (red real)
- **Estudiantes WiFi:** Conectados a red 192.168.1.x
- **Resultado:** Sin conectividad entre redes

### Causa Raíz:
El router intranet cambió de configuración y ahora funciona en la red 192.168.1.x en lugar de 172.23.215.x

## ✅ Solución Implementada

### Configuración de Red:
```bash
# IP del servidor cambiada a:
IP: *************/24
Gateway: ***********
DNS: *******, *******

# Comando aplicado:
sudo nmcli connection modify "Wired connection 1" \
    ipv4.addresses "*************/24" \
    ipv4.gateway "***********" \
    ipv4.dns "*******,*******"
```

### Configuración de Moodle:
```php
// Archivo: /var/www/html/moodle/config.php
$CFG->wwwroot = 'http://*************/moodle';
```

## 🌐 Acceso para Estudiantes

### URL de Acceso:
```
http://*************/moodle
```

### Requisitos:
- Tablets conectadas al WiFi "icfesmatematicas1"
- Router funcionando en ***********

## 📊 Estado del Sistema

### Servicios:
- ✅ Apache2: Funcionando
- ✅ MySQL: Funcionando  
- ✅ Moodle: Accesible
- ✅ Red: Conectividad completa

### Conectividad:
- ✅ Servidor ↔ Router: OK
- ✅ WiFi ↔ LAN: OK
- ✅ Tablets ↔ Servidor: OK

## 🔧 Comandos de Verificación

### Verificar IP del servidor:
```bash
ip addr show enp2s0 | grep "inet "
```

### Verificar conectividad con router:
```bash
ping ***********
```

### Verificar Moodle:
```bash
wget --spider http://*************/moodle
```

## 📝 Notas Importantes

1. **Red estable:** Configuración fija, no depende de DHCP
2. **Misma red:** Servidor y estudiantes en 192.168.1.x
3. **Router funcional:** Gateway *********** responde correctamente
4. **Configuración permanente:** Cambios guardados en NetworkManager

## 🆘 Solución de Problemas

### Si no funciona:
1. Verificar que router esté encendido
2. Verificar cable de red conectado
3. Verificar que tablets se conecten al WiFi
4. Verificar IP del servidor: `ip addr show enp2s0`

### Para volver a configuración anterior:
```bash
sudo nmcli connection modify "Wired connection 1" \
    ipv4.addresses "*************/24" \
    ipv4.gateway "************"
```

---
**✅ Problema resuelto exitosamente**  
**🌐 Moodle accesible en: http://*************/moodle**
