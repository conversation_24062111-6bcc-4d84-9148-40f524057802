<?php
// Script para aplicar optimizaciones a Moodle config.php

$config_file = '/var/www/html/moodle/config.php';
$backup_file = '/var/www/html/moodle/config.php.backup';

// Leer el archivo actual
$content = file_get_contents($config_file);

// Optimizaciones a agregar
$optimizations = '
// ===== OPTIMIZACIONES DE RENDIMIENTO =====

// Configuración de cache
$CFG->cachedir = \'/var/www/moodledata/cache\';
$CFG->localcachedir = \'/var/www/moodledata/localcache\';
$CFG->tempdir = \'/var/www/moodledata/temp\';

// Optimizaciones de rendimiento
$CFG->enablecompletion = true;
$CFG->completiondefault = true;

// Configuración de sesiones optimizada
$CFG->dbsessions = false; // Usar archivos en lugar de DB para sesiones
$CFG->sessiontimeout = 7200; // 2 horas

// Configuración de logs optimizada
$CFG->loglifetime = 30; // Mantener logs por 30 días

// Configuración de cache de strings
$CFG->langstringcache = true;

// Optimización de CSS y JS
$CFG->cachejs = true;
$CFG->themedesignermode = false; // IMPORTANTE: debe estar en false en producción

// Configuración de filtros optimizada
$CFG->filteruploadedfiles = false; // Desactivar si no es necesario

// ===== FIN OPTIMIZACIONES =====

';

// Buscar la línea require_once e insertar antes
$pattern = '/require_once\(__DIR__ \. \'\/lib\/setup\.php\'\);/';
$replacement = $optimizations . 'require_once(__DIR__ . \'/lib/setup.php\');';

$new_content = preg_replace($pattern, $replacement, $content);

// Escribir el archivo modificado
file_put_contents($config_file, $new_content);

echo "Optimizaciones aplicadas a Moodle config.php\n";
?>
