#!/bin/bash

# Script de monitoreo para Moodle optimizado
# Ejecutar con: bash monitor_moodle.sh

echo "=========================================="
echo "    MONITOR DE RENDIMIENTO MOODLE"
echo "=========================================="
echo "Fecha: $(date)"
echo ""

# 1. Uso de disco
echo "1. USO DE DISCO:"
df -h / | grep -v "Filesystem"
echo ""

# 2. Uso de memoria
echo "2. USO DE MEMORIA:"
free -h | grep -E "(Mem|Inter)"
echo ""

# 3. Procesos de servicios
echo "3. SERVICIOS ACTIVOS:"
echo "Apache: $(ps aux | grep apache2 | grep -v grep | wc -l) procesos"
echo "PHP-FPM: $(ps aux | grep php-fpm | grep -v grep | wc -l) procesos"
echo "MySQL: $(ps aux | grep mysql | grep -v grep | wc -l) procesos"
echo ""

# 4. Carga del sistema
echo "4. CARGA DEL SISTEMA:"
uptime
echo ""

# 5. Conexiones MySQL
echo "5. CONEXIONES MYSQL:"
mysql -u root -p -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null || echo "No se pudo conectar a MySQL (requiere contraseña)"
echo ""

# 6. Tamaño de cache de Moodle
echo "6. CACHE DE MOODLE:"
if [ -d "/var/www/moodledata/cache" ]; then
    echo "Cache principal: $(du -sh /var/www/moodledata/cache 2>/dev/null | cut -f1)"
fi
if [ -d "/var/www/moodledata/localcache" ]; then
    echo "Cache local: $(du -sh /var/www/moodledata/localcache 2>/dev/null | cut -f1)"
fi
echo ""

# 7. Logs de errores recientes
echo "7. ERRORES RECIENTES (últimas 5 líneas):"
echo "Apache:"
tail -5 /var/log/apache2/error.log 2>/dev/null | grep -v "AH00558" || echo "No hay errores recientes"
echo ""

echo "=========================================="
echo "Monitoreo completado - $(date)"
echo "=========================================="
