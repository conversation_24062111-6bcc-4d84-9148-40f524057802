#!/bin/bash

# Script para configurar Apache para aceptar conexiones desde cualquier IP
echo "🔧 Configurando Apache para aceptar cualquier IP..."

# Backup del archivo original
sudo cp /etc/apache2/sites-available/000-default.conf /etc/apache2/sites-available/000-default.conf.backup.$(date +%Y%m%d_%H%M%S)

# Crear nueva configuración
sudo tee /etc/apache2/sites-available/000-default.conf > /dev/null << 'EOF'
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html

    # Configuración para aceptar cualquier IP/dominio
    ServerName localhost
    ServerAlias *

    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # Configuración específica para Moodle
    <Directory /var/www/html/moodle>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Permitir archivos .htaccess
        AllowOverride FileInfo AuthConfig Limit Indexes Options
        
        # Configuración de PHP para Moodle
        php_value upload_max_filesize 100M
        php_value post_max_size 100M
        php_value max_execution_time 300
        php_value max_input_vars 5000
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF

echo "✅ Configuración de Apache actualizada"

# Verificar configuración
echo "🔍 Verificando configuración de Apache..."
sudo apache2ctl configtest

if [ $? -eq 0 ]; then
    echo "✅ Configuración válida"
    
    # Reiniciar Apache
    echo "🔄 Reiniciando Apache..."
    sudo systemctl reload apache2
    
    if [ $? -eq 0 ]; then
        echo "✅ Apache reiniciado correctamente"
        echo ""
        echo "🌐 CONFIGURACIÓN COMPLETADA"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "✅ Moodle ahora es accesible desde cualquier IP"
        echo "📱 Los estudiantes pueden acceder usando:"
        echo "   • http://[IP_DEL_SERVIDOR]/moodle"
        echo "   • http://[NOMBRE_DEL_SERVIDOR]/moodle"
        echo "   • http://localhost/moodle (desde el servidor)"
        echo ""
        echo "🔍 Para verificar la IP actual del servidor:"
        echo "   ip addr show | grep 'inet '"
        echo ""
    else
        echo "❌ Error al reiniciar Apache"
        exit 1
    fi
else
    echo "❌ Error en la configuración de Apache"
    exit 1
fi
